# 更新日志

## 2024-12-21

### 修复 HW181-MIC 校准数据保存问题

- **修复**: 解决了 HW181-MIC 校准数据无法保存的问题
- **优化**: 增强了 Preferences 存储的稳定性和错误处理
- **新增**: 支持多命名空间存储策略，提高数据保存成功率
- **新增**: HW181-MIC 传感器现在支持自动校准功能
- **优化**: 传感器初始化时如果未校准会自动进行快速校准（15秒）
- **智能**: 自动校准包含合理性验证，确保校准结果的准确性
- **修改**: 将 MQTT 上传中的 `voltage` 字段改名为 `sound_voltage`，更准确地表示"声音的电压"
- **移除**: 从 `MicData` 结构中移除了 `max_db` 字段
- **移除**: 从 MQTT 数据上传中移除了 `max_decibels` 字段（保持与 OneNET 平台要求一致）
- **移除**: 从传感器管理器中移除了 `max_db_in_period` 相关的计算和统计代码
- **保持**: 其他所有字段保持不变：
  - `decibels` - 当前分贝值（浮点数）
  - `sound_detected` - 声音检测状态（布尔值转整数）
  - `sound_voltage` - 声音电压值（浮点数）
  - `min_db` - 期间最小分贝（内部统计，不上传）
  - `avg_db` - 期间平均分贝（内部统计，不上传）

### 自动校准功能

HW181-MIC 传感器现在支持智能自动校准：

1. **启动时自动校准**：如果传感器未校准，系统会在初始化时自动进行校准
2. **快速校准**：自动校准只需 15 秒，比手动校准（60秒）更快
3. **智能验证**：自动校准包含合理性检查，确保基线值在有效范围内
4. **无感知体验**：用户无需手动执行任何命令，设备即可正常工作

自动校准过程：

- 采集 300 个样本（每 50ms 一次，共 15 秒）
- 计算平均基线值和变化阈值
- 验证校准结果的合理性
- 自动保存校准数据到 Flash 存储器

### 校准方式对比

| 校准方式 | 触发条件 | 时长 | 样本数 | 适用场景 |
|---------|----------|------|--------|----------|
| 自动校准 | 传感器未校准时自动触发 | 15秒 | 300个 | 首次使用、快速部署 |
| 手动校准 | 用户执行 `mic_cal` 命令 | 60秒 | 1200个 | 精确校准、重新校准 |

### 数据格式说明

上传到 OneNET 的 HW181-MIC 数据格式：

```json
{
  "decibels": { "value": 45.2, "time": 1640088400 },
  "sound_detected": { "value": 1, "time": 1640088400 },
  "sound_voltage": { "value": 1.234, "time": 1640088400 }
}
```

### 编译验证

- ✅ 所有传感器组合下编译通过
- ✅ AsyncMqttClient 集成正常
- ✅ 命令行接口功能完整
- ✅ 条件编译宏正常工作

### 命令说明

- `show_mic` 或 `mic` - 显示 HW181-MIC 传感器数据（不包含 max_decibels）
- `mic_status` 或 `mstatus` - 显示校准状态
- `mic_cal` - 执行分贝校准

### 校准数据存储优化

为了解决校准数据保存失败的问题，我们实现了多层存储策略：

#### 存储策略
1. **专用命名空间**: 首选使用 `hw181_calib` 命名空间存储校准数据
2. **备用命名空间**: 如果专用命名空间失败，自动切换到 `nvs` 默认命名空间
3. **详细诊断**: 提供详细的错误信息和可能的解决方案

#### 错误处理增强
- 显示具体的写入字节数，帮助诊断存储问题
- 提供可能的失败原因分析
- 自动尝试备用存储策略

#### 命令支持
- `mic_cal` - 手动校准（60秒高精度）
- `mic_status` - 显示校准状态
- `mic_del_cal` - 清除校准数据
- `show_mic` - 显示当前传感器数据

现在校准数据保存成功率显著提高，即使在 Flash 存储空间紧张的情况下也能正常工作。
