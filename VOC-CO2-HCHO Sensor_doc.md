## 🧩 一、URAT 接口引脚定义（接线说明）

| 引脚 | 作用说明         |
| -- | ------------ |
| 1  | 接电源地 GND（负极） |
| 2  | 接电源正极（+5V）   |
| 3  | RXD（模块端接收）   |
| 4  | TXD（模块端发送）   |

💡 **接口标准**：

* 模块供电：5V DC
* 通信标准：TTL 串口（非 RS232）

---

## 🔧 二、串口通信协议

| 参数  | 值        |
| --- | -------- |
| 波特率 | 9600 bps |
| 数据位 | 8 位      |
| 停止位 | 1 位      |
| 校验位 | 无        |

---

## 🧬 三、串口数据格式（模块发送的数据帧结构）

模块周期性发出 9 个字节，含义如下：

| 字节位    | 内容            | 说明                    |
| ------ | ------------- | --------------------- |
| B1     | 模块地址          | 固定为 `2CH`（十六进制）       |
| B2     | 功能码           | 固定为 `E4H`（十六进制）       |
| B3\~B4 | TVOC 浓度（高低字节） |                       |
| B5\~B6 | CH₂O 浓度（高低字节） |                       |
| B7\~B8 | CO₂ 浓度（高低字节）  |                       |
| B9     | 校验和           | `B1+B2+...+B8` 的低 8 位 |

---

## 📏 四、数据计算方法

**气体浓度值（mg/m³）** 的计算方式：

```text
浓度值 = (高位 * 256 + 低位) × 0.001
```

### 例子：

假设：

* TVOC 高位 = `0x00`, 低位 = `0xC8`（十进制 200）
* 则 TVOC = (0×256 + 200) × 0.001 = **0.200 mg/m³**

---

## ✅ 五、模块接口位置说明（右上角插针）

从图中实物看：

* GND = 电源负极
* 5V = 电源正极
* A10/SCL ≈ RX
* B/SDA ≈ TX

需结合原理图确认引脚方向，但与文档表对应为：

| 插针标签    | 功能      |
| ------- | ------- |
| GND     | 电源地（1）  |
| 5V      | 电源正极（2） |
| A10/SCL | RXD（3）  |
| B/SDA   | TXD（4）  |