# VOC-CO2-HCH<PERSON>三合一传感器测试程序

## 概述

这是一个专门用于测试VOC-CO2-HCHO三合一传感器的独立程序，可以检测TVOC浓度、甲醛浓度和CO₂浓度。

## 传感器功能

### 检测参数
- **TVOC浓度**: 0-65.535 mg/m³ (精度0.001)
- **甲醛(CH₂O)浓度**: 0-65.535 mg/m³ (精度0.001)
- **CO₂浓度**: 0-65.535 mg/m³ (精度0.001)

### 数据质量评估标准

#### TVOC浓度等级
- **优秀**: ≤0.3 mg/m³ ✅
- **良好**: 0.3-0.6 mg/m³ 🟢
- **一般**: 0.6-1.0 mg/m³ 🟡
- **较差**: 1.0-3.0 mg/m³ 🟠
- **很差**: >3.0 mg/m³ 🔴

#### 甲醛安全等级
- **安全**: ≤0.08 mg/m³ ✅
- **可接受**: 0.08-0.10 mg/m³ 🟢
- **需关注**: 0.10-0.12 mg/m³ 🟡
- **危险**: 0.12-0.15 mg/m³ 🔴
- **极危险**: >0.15 mg/m³ ⚠️

#### CO₂浓度等级
- **新鲜空气**: ≤0.7 mg/m³ ✅
- **可接受**: 0.7-1.0 mg/m³ 🟢
- **令人困倦**: 1.0-1.8 mg/m³ 🟡
- **闷热**: 1.8-2.7 mg/m³ 🟠
- **非常闷热**: >2.7 mg/m³ 🔴

## 硬件连接

### 接线图
```
VOC-CO2-HCHO传感器    ESP32C3
VCC                -> 3.3V (或5V)
GND                -> GND
TX                 -> GPIO 1 (RX)
RX                 -> GPIO 0 (TX)
```

### 通信参数
- **波特率**: 9600
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无 (8N1)
- **数据帧长度**: 9字节
- **模块地址**: 0x2C
- **功能码**: 0xE4

## 使用方法

### 1. 编译和上传
```bash
# 使用专用配置文件编译
pio run -c platformio_21voc.ini

# 上传到ESP32C3
pio run -c platformio_21voc.ini --target upload

# 监控串口输出
pio run -c platformio_21voc.ini --target monitor
```

### 2. 串口命令
程序支持以下串口命令：

- **`stats`** - 显示详细统计信息
- **`debug`** - 调试UART连接状态
- **`test`** - 执行传感器连接测试
- **`reset`** - 重置统计数据
- **`help`** - 显示帮助信息

### 3. 运行流程
1. 连接硬件并上传程序
2. 打开串口监控器 (波特率: 115200)
3. 等待传感器预热 (2-3分钟)
4. 观察实时数据输出和质量评估
5. 使用串口命令进行调试和统计

## 输出示例

### 正常数据输出
```
========================================
⏰ [45230 ms] 开始读取VOC-CO2-HCHO传感器数据
📡 接收到 9 字节: 2C E4 01 2C 00 1E 00 B4 A2
🔍 解析数据: 长度=9 找到帧头0x2C 0xE4在位置0
数据帧: 2C E4 01 2C 00 1E 00 B4 A2
✅ 解析成功:
  TVOC: 0.300 mg/m³
  甲醛: 0.030 mg/m³
  CO₂: 0.180 mg/m³

📊 === VOC-CO2-HCHO传感器读数 ===
⏰ 时间戳: 45230 ms
🌿 TVOC浓度: 0.300 mg/m³
🏠 甲醛(CH₂O): 0.030 mg/m³
💨 CO₂浓度: 0.180 mg/m³
===============================

🔍 === 数据质量评估 ===
🌿 TVOC浓度: 优秀 ✅ (0.300 mg/m³)
🏠 甲醛安全性: 安全 ✅ (0.030 mg/m³)
💨 CO₂浓度: 新鲜空气 ✅ (0.180 mg/m³)
=====================

✅ 数据读取和处理完成
📊 统计: 总计15次, 成功14次, 错误1次, 成功率93.3%
```

### 统计报告示例
```
📊 ========== 统计报告 ==========
📈 数据概况: 总计50次读取, 成功47次, 错误3次
✅ 成功率: 94.0%

🌿 TVOC浓度统计 (mg/m³):
   平均值: 0.265
   最小值: 0.180
   最大值: 0.420

🏠 甲醛(CH₂O)统计 (mg/m³):
   平均值: 0.058
   最小值: 0.025
   最大值: 0.095

💨 CO₂浓度统计 (mg/m³):
   平均值: 0.450
   最小值: 0.200
   最大值: 0.850
================================
```

## 故障排除

### 常见问题

#### 1. 无法接收数据
- 检查接线是否正确
- 确认传感器供电正常
- 检查波特率设置
- 使用 `debug` 命令查看UART状态

#### 2. 数据解析失败
- 检查数据帧格式
- 确认帧头0x2C 0xE4是否正确
- 检查数据长度是否为9字节

#### 3. 数据验证失败
- 检查数据是否在合理范围内
- 确认传感器是否充分预热
- 检查环境条件是否正常

#### 4. 成功率低
- 检查UART连接稳定性
- 确认电源供电稳定
- 检查环境电磁干扰

### 调试技巧

1. **使用debug命令**: 输入 `debug` 查看UART连接状态
2. **执行传感器测试**: 输入 `test` 进行连接测试
3. **查看统计信息**: 输入 `stats` 了解数据质量
4. **重置统计**: 输入 `reset` 清除历史数据

## 技术参数

### 传感器规格
- **工作电压**: 3.3V-5V
- **工作电流**: <50mA
- **响应时间**: <30秒
- **预热时间**: 2-3分钟
- **工作温度**: -10°C to +50°C
- **存储温度**: -40°C to +85°C

### ESP32C3配置
- **UART**: UART1
- **波特率**: 9600 bps
- **数据格式**: 8N1
- **缓冲区**: 64字节
- **超时**: 2秒

## 扩展功能

可以在此基础上添加：
- WiFi连接和数据上传
- LCD显示屏显示
- 数据记录和分析
- 报警和通知功能
- 多传感器组合监测
- Web界面监控

## 技术支持

如遇到问题，请检查：
1. 硬件连接是否正确
2. 传感器供电是否稳定
3. UART通信参数是否匹配
4. 传感器是否充分预热

更多技术文档请参考传感器数据手册和ESP32C3开发指南。
