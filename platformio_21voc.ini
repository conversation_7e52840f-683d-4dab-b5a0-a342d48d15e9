; PlatformIO Project Configuration File for 21VOC Sensor Test
;
; 专门用于测试21VOC五合一传感器的配置文件
; 使用方法: pio run -c platformio_21voc.ini

[env:airm2m_core_esp32c3_21voc]
platform = espressif32
board = airm2m_core_esp32c3
framework = arduino
monitor_speed = 115200

; 指定源文件 - 只编译21VOC传感器测试程序
build_src_filter =
    -<*>
    +<main_VOC-CO2-HCHO-Sensor.cpp>

; 21VOC传感器测试不需要额外的库依赖
; lib_deps =

; 构建标志 - 启用21VOC传感器
build_flags = 
    -DENABLE_21VOC=1
    -DENABLE_BMP280=0
    -DENABLE_MQ135=0
    -DENABLE_HW181_MIC=0

; 监控配置
monitor_filters = 
    esp32_exception_decoder
    time

; 上传配置
upload_speed = 921600
