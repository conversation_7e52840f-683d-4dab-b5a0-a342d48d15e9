# HW181-MIC 分贝检测模块集成文档

## 概述

HW181-MIC 分贝检测模块已成功集成到传感器组合中，提供实时声音环境监测功能。

## 硬件连接

```
HW181-MIC 模块连接:
- VCC: 5V 或 3.3V
- GND: GND
- 模拟输出: GPIO3 (ADC1_CH0)
```

## 传感器配置

在 `platformio.ini` 中启用 HW181-MIC：

```ini
build_flags = 
    -DENABLE_HW181_MIC=1    ; 启用HW181-MIC分贝检测模块
```

## 功能特性

### 📊 数据监测
- **分贝值计算**: 实时计算环境噪音分贝值 (30-120 dB)
- **声音检测**: 基于变化阈值的声音事件检测
- **电压监测**: ADC电压值监测 (0-3.3V)
- **统计分析**: 最大、最小、平均分贝值统计

### 🔧 智能校准系统

#### 自动校准（推荐）
- **触发条件**: 传感器首次使用或未找到校准数据时自动启动
- **校准时长**: 15 秒快速校准
- **采样策略**: 300 个样本，每 50ms 采集一次
- **智能验证**: 自动检查校准结果合理性（基线值 100-4000 范围）
- **无感知体验**: 用户无需手动操作，设备自动完成校准

#### 手动校准（高精度）
- **触发方式**: 执行 `mic_cal` 命令
- **校准时长**: 60 秒精确校准
- **采样策略**: 1200 个样本，每 50ms 采集一次
- **适用场景**: 需要高精度校准或重新校准环境基线

#### 校准数据管理
- **持久化存储**: 校准数据保存到 NVS 存储
- **自动加载**: 重启后自动加载校准数据
- **数据清除**: 使用 `mic_del_cal` 命令清除校准数据

### 📡 MQTT 数据上传
HW181-MIC 数据会自动包含在 OneNET 上报数据中：

```json
{
  "params": {
    "decibels": {
      "value": 45.2,
      "time": 1642089600000
    },
    "sound_detected": {
      "value": 1,
      "time": 1642089600000
    },
    "voltage": {
      "value": 1.65,
      "time": 1642089600000
    },
    "max_decibels": {
      "value": 52.3,
      "time": 1642089600000
    }
  }
}
```

## 命令行接口

### 校准命令
- `mic_cal` 或 `mcal` - 开始 HW181-MIC 传感器校准
- `mic_status` 或 `mstatus` - 显示校准状态
- `delete_mic_cal` 或 `dmic` - 删除校准数据

### 数据查看命令
- `show_mic` 或 `mic` - 显示当前传感器数据
- `help` - 显示所有可用命令

## 数据字段说明

### 上报字段
| 字段名 | 类型 | 单位 | 说明 |
|--------|------|------|------|
| `decibels` | Float | dB | 实时分贝值 |
| `sound_detected` | Integer | - | 声音检测状态 (0/1) |
| `voltage` | Float | V | 模拟输出电压 |
| `max_decibels` | Float | dB | 统计期间最大分贝值 |

### 内部数据结构 (`MicData`)
```cpp
struct MicData {
  float decibels;          // 分贝值
  float voltage;           // 电压值
  int analog_value;        // 模拟值 (0-4095)
  bool sound_detected;     // 声音检测标志
  float max_db;            // 期间最大分贝
  float min_db;            // 期间最小分贝
  float avg_db;            // 期间平均分贝
  bool calibrated;         // 校准状态
  bool valid;              // 数据有效性
  unsigned long timestamp; // 时间戳
};
```

## 算法说明

### 分贝计算算法
1. **电压比值法**: 基于基线电压的相对变化计算
2. **模拟值映射法**: 线性映射模拟值到分贝范围
3. **变化响应法**: 基于信号变化幅度的快速响应
4. **组合算法**: 三种方法加权组合 (40% + 40% + 20%)

### 平滑滤波
- **自适应平滑**: 根据变化幅度调整平滑系数
- **大变化快响应**: 变化 >5dB 时提高响应度
- **小变化强平滑**: 变化 <1dB 时增强平滑效果

### 声音检测
- **阈值检测**: 基于校准得出的自适应阈值
- **变化量判断**: 当前值与上一次值的差值比较
- **校准状态要求**: 仅在校准完成后进行检测

## 环境评级

根据分贝值自动评估环境噪音等级：

| 分贝范围 | 环境评级 | 典型场景 |
|----------|----------|----------|
| < 40 dB | 很安静 | 图书馆级别 |
| 40-50 dB | 安静 | 住宅区 |
| 50-60 dB | 正常 | 办公室 |
| 60-70 dB | 稍嘈杂 | 街道 |
| 70-80 dB | 嘈杂 | 繁忙街道 |
| 80-90 dB | 很嘈杂 | 施工现场 |
| > 90 dB | 极嘈杂 | 可能损害听力 |

## 校准流程

### 自动校准步骤
1. **环境准备**: 确保环境安静（60秒内无声音）
2. **数据采集**: 以50ms间隔采集1200个样本
3. **基线计算**: 计算模拟值平均值作为基线
4. **阈值确定**: 根据信号变化量计算自适应阈值
5. **数据保存**: 将校准结果保存到 NVS 存储

### 校准指标
- **采样时间**: 60秒 (1200样本 × 50ms)
- **最小阈值**: 30 (防止过于敏感)
- **阈值计算**: 平均变化量 × 3
- **存储位置**: NVS "hw181_calib" 命名空间

## 故障排查

### 常见问题
1. **未检测到声音**: 检查连线、校准状态、环境噪音
2. **持续检测声音**: 可能环境噪音过大或阈值过低
3. **分贝值异常**: 重新校准或检查模块供电
4. **校准失败**: 确保校准期间环境安静

### 调试信息
系统提供详细的调试输出，包括：
- 实时分贝值和电压
- 声音检测状态和统计
- 校准进度和结果
- 错误诊断和建议

## 集成验证

编译并上传固件后，可以通过以下方式验证集成：

1. **串口监视器**: 查看传感器初始化和数据输出
2. **校准测试**: 使用 `mic_cal` 命令进行校准
3. **数据验证**: 使用 `show_mic` 查看实时数据
4. **MQTT验证**: 检查 OneNET 平台是否收到分贝数据

## 性能影响

- **内存占用**: 约增加 2KB RAM
- **Flash占用**: 约增加 15KB
- **CPU占用**: 每次读取约 1-2ms
- **功耗影响**: 模拟读取功耗可忽略

## 版本兼容性

- **ESP32C3**: 完全支持
- **Arduino Framework**: 兼容
- **AsyncMqttClient**: 兼容
- **OneNET 平台**: 兼容物模型格式
