# HW181-MIC 自动校准使用指南

## 🎉 自动校准功能

从现在开始，HW181-MIC 分贝检测模块支持全自动校准，无需手动操作！

## 📋 工作流程

### 1. 首次启动（未校准状态）
```
=== 初始化HW181-MIC分贝检测模块 ===
引脚配置: GPIO3 (模拟输入)
⚠️ HW181-MIC传感器未校准，开始自动校准...
💡 请保持环境相对安静，校准将自动进行

=== 开始HW181-MIC自动校准 ===
📊 正在采集基线数据，请稍候...
📈 自动校准进度: 33%
📈 自动校准进度: 66%
📈 自动校准进度: 100%

✅ 自动校准完成!
📊 基线值: 1234 (0.998V)
🎚️ 变化阈值: 45
💾 校准数据已保存到Flash存储器
🔊 传感器现在可以正常检测声音分贝

🎉 HW181-MIC传感器自动校准成功！
```

### 2. 后续启动（已校准状态）
```
=== 初始化HW181-MIC分贝检测模块 ===
引脚配置: GPIO3 (模拟输入)
📁 加载HW181-MIC校准数据成功
✅ HW181-MIC传感器已校准，基线值: 1234
```

## 🔄 校准对比

| 功能特性 | 自动校准 | 手动校准 |
|---------|----------|----------|
| **触发方式** | 系统自动 | 手动命令 `mic_cal` |
| **校准时间** | 15 秒 | 60 秒 |
| **采样数量** | 300 个 | 1200 个 |
| **用户交互** | 无需操作 | 需要执行命令 |
| **适用场景** | 首次部署、快速启动 | 精确校准、环境变化 |
| **精度等级** | 标准精度 | 高精度 |

## 📊 数据上报

自动校准完成后，传感器数据会立即上报到 OneNET 平台：

```json
{
  "params": {
    "decibels": { "value": 42.5, "time": 1640088400000 },
    "sound_detected": { "value": 0, "time": 1640088400000 },
    "sound_voltage": { "value": 1.023, "time": 1640088400000 }
  }
}
```

## 🛠️ 故障排除

### 自动校准失败
如果自动校准失败，系统会显示：
```
❌ HW181-MIC传感器自动校准失败，可使用 'mic_cal' 手动校准
```

**解决方案**：
1. 检查传感器硬件连接
2. 确保 GPIO3 引脚正确连接
3. 使用 `mic_cal` 命令进行手动校准
4. 检查环境是否过于嘈杂

### 重新校准
如需重新校准环境基线：
```bash
mic_del_cal   # 删除现有校准数据
# 重启设备，系统会自动重新校准
```

或者直接手动校准：
```bash
mic_cal       # 进行60秒精确校准
```

## 💡 最佳实践

1. **环境选择**: 在相对安静的环境中启动设备
2. **等待完成**: 让自动校准完整执行完毕（15秒）
3. **验证功能**: 使用 `show_mic` 命令查看传感器数据
4. **定期检查**: 如环境显著变化，可重新校准

## 🎯 优势特点

- ✅ **零配置**: 无需任何手动设置
- ✅ **快速启动**: 15秒即可完成校准
- ✅ **智能验证**: 自动检查校准结果合理性
- ✅ **持久保存**: 校准数据自动保存，重启后生效
- ✅ **兼容性**: 保持与手动校准的完全兼容

现在您可以直接启动设备，HW181-MIC 传感器会自动完成校准并开始工作！
