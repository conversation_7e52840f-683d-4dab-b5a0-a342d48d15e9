# VOC传感器单位转换总结

## 修改概述

已将VOC-CO2-HCHO三合一传感器的数据单位从毫克(mg/m³)转换为微克(μg/m³)，以保持与OneNET平台和用户界面的一致性。

## 转换比例

**1 mg/m³ = 1000 μg/m³**

所有内部存储仍使用mg/m³，但在显示和上传时转换为μg/m³。

## 修改的文件和内容

### 1. MQTT数据上传 (`src/mqtt_manager.cpp`)

**修改前**:
```cpp
doc["params"]["voc_ugm3"]["value"] = data.voc_co2_hcho_data.tvoc_mgm3;
doc["params"]["ch2o_ugm3"]["value"] = data.voc_co2_hcho_data.ch2o_mgm3;
doc["params"]["eco2_ppm"]["value"] = data.voc_co2_hcho_data.co2_mgm3;
```

**修改后**:
```cpp
doc["params"]["voc_ugm3"]["value"] = data.voc_co2_hcho_data.tvoc_mgm3 * 1000;  // 转换为 μg/m³
doc["params"]["ch2o_ugm3"]["value"] = data.voc_co2_hcho_data.ch2o_mgm3 * 1000; // 转换为 μg/m³
doc["params"]["eco2_ppm"]["value"] = data.voc_co2_hcho_data.co2_mgm3 * 1000;   // 转换为 μg/m³
```

### 2. 主程序数据显示 (`src/main.cpp`)

**修改前**:
```cpp
Serial.printf("TVOC浓度: %.3f mg/m³\n", data.voc_co2_hcho_data.tvoc_mgm3);
Serial.printf("甲醛浓度: %.3f mg/m³\n", data.voc_co2_hcho_data.ch2o_mgm3);
Serial.printf("CO₂浓度: %.3f mg/m³\n", data.voc_co2_hcho_data.co2_mgm3);
```

**修改后**:
```cpp
Serial.printf("TVOC浓度: %.0f μg/m³\n", data.voc_co2_hcho_data.tvoc_mgm3 * 1000);
Serial.printf("甲醛浓度: %.0f μg/m³\n", data.voc_co2_hcho_data.ch2o_mgm3 * 1000);
Serial.printf("CO₂浓度: %.0f μg/m³\n", data.voc_co2_hcho_data.co2_mgm3 * 1000);
```

### 3. 传感器读数显示 (`src/sensors.cpp`)

**修改前**:
```cpp
Serial.printf("🌿 TVOC浓度: %.3f mg/m³\n", data.tvoc_mgm3);
Serial.printf("🏠 甲醛(CH₂O): %.3f mg/m³\n", data.ch2o_mgm3);
Serial.printf("💨 CO₂浓度: %.3f mg/m³\n", data.co2_mgm3);
```

**修改后**:
```cpp
Serial.printf("🌿 TVOC浓度: %.0f μg/m³\n", data.tvoc_mgm3 * 1000);
Serial.printf("🏠 甲醛(CH₂O): %.0f μg/m³\n", data.ch2o_mgm3 * 1000);
Serial.printf("💨 CO₂浓度: %.0f μg/m³\n", data.co2_mgm3 * 1000);
```

### 4. 数据质量评估 (`src/sensors.cpp`)

**TVOC评估标准** (转换为μg/m³):
- 优秀: ≤300 μg/m³ (原0.3 mg/m³)
- 良好: 301-600 μg/m³ (原0.3-0.6 mg/m³)
- 一般: 601-1000 μg/m³ (原0.6-1.0 mg/m³)
- 较差: 1001-3000 μg/m³ (原1.0-3.0 mg/m³)
- 很差: >3000 μg/m³ (原>3.0 mg/m³)

**甲醛评估标准** (转换为μg/m³):
- 安全: ≤80 μg/m³ (原0.08 mg/m³)
- 可接受: 81-100 μg/m³ (原0.08-0.10 mg/m³)
- 需关注: 101-120 μg/m³ (原0.10-0.12 mg/m³)
- 危险: 121-150 μg/m³ (原0.12-0.15 mg/m³)
- 极危险: >150 μg/m³ (原>0.15 mg/m³)

**CO₂评估标准** (转换为μg/m³):
- 新鲜空气: ≤700 μg/m³ (原0.7 mg/m³)
- 可接受: 701-1000 μg/m³ (原0.7-1.0 mg/m³)
- 令人困倦: 1001-1800 μg/m³ (原1.0-1.8 mg/m³)
- 闷热: 1801-2700 μg/m³ (原1.8-2.7 mg/m³)
- 非常闷热: >2700 μg/m³ (原>2.7 mg/m³)

## OneNET数据格式

现在上传到OneNET的数据格式为：

```json
{
  "params": {
    "voc_ugm3": {"value": 300, "time": 1640995200000},      // TVOC浓度 (μg/m³)
    "ch2o_ugm3": {"value": 30, "time": 1640995200000},      // 甲醛浓度 (μg/m³)
    "eco2_ppm": {"value": 180, "time": 1640995200000}       // CO₂浓度 (μg/m³)
  }
}
```

## 显示效果

### 串口输出示例
```
📊 === VOC-CO2-HCHO传感器读数 ===
⏰ 时间戳: 45230 ms
🌿 TVOC浓度: 300 μg/m³
🏠 甲醛(CH₂O): 30 μg/m³
💨 CO₂浓度: 180 μg/m³
===============================

🔍 === 数据质量评估 ===
🌿 TVOC浓度: 优秀 ✅ (300 μg/m³)
🏠 甲醛安全性: 安全 ✅ (30 μg/m³)
💨 CO₂浓度: 新鲜空气 ✅ (180 μg/m³)
=====================
```

### 主程序数据显示
```
--- VOC-CO2-HCHO三合一传感器数据 ---
TVOC浓度: 300 μg/m³
甲醛浓度: 30 μg/m³
CO₂浓度: 180 μg/m³
```

## 当前配置状态

- **BMP280传感器**: ✅ 启用
- **VOC-CO2-HCHO传感器**: ✅ 启用
- **其他传感器**: ❌ 禁用

## 编译结果

- **RAM使用**: 12.1% (39,764 bytes)
- **Flash使用**: 63.6% (833,434 bytes)
- **编译状态**: ✅ 成功

## 技术说明

1. **内部存储**: 传感器数据在内部仍以mg/m³存储，保持原始精度
2. **显示转换**: 仅在显示和上传时进行单位转换
3. **精度保持**: 转换后显示为整数，避免小数点混淆
4. **一致性**: 与21VOC传感器的μg/m³单位保持一致

## 使用方法

1. **编译上传**:
   ```bash
   pio run --target upload
   pio run --target monitor
   ```

2. **查看数据**:
   ```
   show_voc  # 显示VOC-CO2-HCHO传感器数据
   ```

3. **数据验证**:
   - 串口输出显示μg/m³单位
   - OneNET平台接收μg/m³数据
   - 质量评估基于μg/m³标准

现在所有VOC相关数据都统一使用μg/m³单位，确保了数据的一致性和可读性。
