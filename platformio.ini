; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:airm2m_core_esp32c3]
platform = espressif32
board = airm2m_core_esp32c3
framework = arduino
monitor_speed = 115200

; NVS 分区配置
board_build.partitions = default.csv
board_build.filesystem = littlefs

lib_deps =
    phoenix1747/MQ135
    adafruit/Adafruit BMP280 Library@^2.6.8
    adafruit/Adafruit Unified Sensor@^1.1.14
    marvinroger/AsyncMqttClient@^0.9.0
    esphome/ESPAsyncTCP-esphome@^1.2.3
    bblanchon/Arduino<PERSON>son@^7.0.4

; 传感器配置 - 用户可以通过修改这些标志来自定义启用哪些传感器
; 设置为1启用，设置为0禁用
; 测试配置：启用所有传感器
build_flags = 
    -DENABLE_BMP280=1       ; BMP280气压温度传感器 (I2C)
    -DENABLE_MQ135=0        ; MQ135空气质量传感器 (ADC)
    -DENABLE_21VOC=0        ; 21VOC五合一传感器 (UART)
    -DENABLE_HW181_MIC=0    ; HW181-MIC分贝检测模块 (ADC)
    -DENABLE_VOC_CO2_HCHO=1 ; VOC、CO2、HCHO传感器 (UART)
