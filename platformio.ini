; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:airm2m_core_esp32c3]
platform = espressif32
board = airm2m_core_esp32c3
framework = arduino
monitor_speed = 115200

lib_deps =
    adafruit/Adafruit BMP280 Library@^2.6.8
    adafruit/Adafruit Unified Sensor@^1.1.14
    knolleary/PubSubClient@^2.8
    bblanchon/Ard<PERSON><PERSON><PERSON><PERSON>@^7.0.4