# VOC-CO2-HCHO三合一传感器快速开始指南

## 硬件连接
```
VOC-CO2-HCHO传感器 -> ESP32C3
VCC               -> 3.3V (或5V)
GND               -> GND
TX                -> GPIO 1 (RX)
RX                -> GPIO 0 (TX)
```

## 快速测试

### 1. 编译和上传
```bash
# 编译21VOC传感器程序
pio run -c platformio_21voc.ini

# 上传到ESP32C3
pio run -c platformio_21voc.ini --target upload

# 监控串口输出
pio run -c platformio_21voc.ini --target monitor
```

### 2. 预期输出
```
========================================
🌿 VOC-CO2-HCHO三合一传感器测试程序
========================================
传感器功能:
  📊 TVOC浓度检测 (mg/m³)
  🏠 甲醛(CH₂O)浓度检测 (mg/m³)
  💨 CO₂浓度检测 (mg/m³)
========================================
硬件连接:
  RX引脚: GPIO 1 (接传感器TX)
  TX引脚: GPIO 0 (接传感器RX)
  波特率: 9600
========================================
🔧 正在初始化UART通信...
✅ UART初始化成功
   配置: 9600 bps, 8N1
   RX: GPIO1, TX: GPIO0
📊 初始化统计数据...
✅ 统计数据初始化完成
✅ Preferences存储初始化成功
🚀 系统初始化完成，开始数据采集...
💡 提示: 传感器需要预热2-3分钟才能获得稳定读数
========================================

========================================
⏰ [5230 ms] 开始读取21VOC传感器数据
📡 接收到 12 字节: 2C 00 32 00 1E 01 90 00 FA 01 2C 8A
🔍 解析数据: 长度=12 找到帧头0x2C在位置0
数据帧: 2C 00 32 00 1E 01 90 00 FA 01 2C 8A
✅ 解析成功:
  VOC: 50 ug/m³
  甲醛: 30 ug/m³
  eCO2: 400 ppm
  温度: 25.0°C
  湿度: 30.0%RH

📊 === 21VOC传感器读数 ===
⏰ 时间戳: 5230 ms
🌿 VOC空气质量: 50 ug/m³
🏠 甲醛(HCHO): 30 ug/m³
💨 eCO2浓度: 400 ppm
🌡️ 温度: 25.0°C
💧 湿度: 30.0%RH
========================

🔍 === 数据质量评估 ===
🌿 VOC空气质量: 优秀 ✅ (50 ug/m³)
🏠 甲醛安全性: 安全 ✅ (30 ug/m³)
💨 eCO2舒适度: 新鲜空气 ✅ (400 ppm)
🌡️ 温度舒适度: 舒适 ✅ (25.0°C)
💧 湿度舒适度: 过干 🏜️ (30.0%RH)
=====================

✅ 数据读取和处理完成
📊 统计: 总计1次, 成功1次, 错误0次, 成功率100.0%
```

## 串口命令

在串口监控器中输入以下命令：

- **`stats`** - 显示详细统计报告
- **`debug`** - 调试UART连接状态  
- **`test`** - 执行传感器连接测试
- **`reset`** - 重置统计数据
- **`help`** - 显示所有可用命令

## 数据质量评估

### TVOC浓度
- ✅ 优秀: ≤0.3 mg/m³
- 🟢 良好: 0.3-0.6 mg/m³
- 🟡 一般: 0.6-1.0 mg/m³
- 🟠 较差: 1.0-3.0 mg/m³
- 🔴 很差: >3.0 mg/m³

### 甲醛安全性
- ✅ 安全: ≤0.08 mg/m³
- 🟢 可接受: 0.08-0.10 mg/m³
- 🟡 需关注: 0.10-0.12 mg/m³
- 🔴 危险: 0.12-0.15 mg/m³
- ⚠️ 极危险: >0.15 mg/m³

### CO₂浓度
- ✅ 新鲜空气: ≤0.7 mg/m³
- 🟢 可接受: 0.7-1.0 mg/m³
- 🟡 令人困倦: 1.0-1.8 mg/m³
- 🟠 闷热: 1.8-2.7 mg/m³
- 🔴 非常闷热: >2.7 mg/m³

## 故障排除

### 无数据输出
1. 检查接线是否正确
2. 确认传感器供电正常
3. 输入 `debug` 命令检查UART状态
4. 输入 `test` 命令执行连接测试

### 数据解析失败
1. 检查波特率是否为9600
2. 确认数据格式为8N1
3. 检查传感器是否支持标准协议

### 数据验证失败
1. 等待传感器充分预热(2-3分钟)
2. 检查环境条件是否正常
3. 确认传感器工作正常

## 技术参数

- **通信协议**: UART
- **波特率**: 9600 bps
- **数据格式**: 8N1
- **数据帧**: 9字节
- **模块地址**: 0x2C
- **功能码**: 0xE4
- **读取间隔**: 2秒
- **统计间隔**: 30秒

## 下一步

测试成功后可以：
1. 集成到完整的环境监测系统
2. 添加WiFi功能上传数据
3. 结合其他传感器
4. 添加显示和报警功能
5. 实现数据记录和分析

详细信息请参考 `21VOC_SENSOR_README.md`
