# HW181-MC 分贝检测模块测试说明

## 模块概述
HW181-MC是一个声音检测传感器模块，具有数字输出和模拟输出功能，广泛用于声音检测、声控开关等应用。

## 连接方式

### 基本连接
| 模块引脚 | ESP32C3引脚 | 说明 |
|---------|-------------|------|
| VCC     | 5V          | 电源正极 |
| GND     | GND         | 电源负极 |
| AO      | GPIO1       | 模拟输出（声音信号强度）|

### 重要说明
1. **模拟输出(AO)**: 输出与声音强度成正比的模拟电压值
2. **GPIO1**: ESP32C3上的ADC1_CH0，支持0-4095的12位模拟读取
3. **灵敏度调节**: 模块上的电位器可调节检测灵敏度
4. **指示灯**: 模块上的LED会在检测到声音时亮起

## 测试功能

### 实时监测
- 每100ms读取一次传感器数据
- 显示数字输出状态和模拟值
- 统计声音检测次数和检测率

### 状态诊断
程序会自动诊断模块状态：
- ✅ **正常工作**: 能够正确检测声音变化
- ⚠️ **未检测到声音**: 可能是连线问题或灵敏度设置
- ⚠️ **持续检测**: 可能是环境噪音或灵敏度过高

### 输出格式
```
🔊 声音检测! 数字值: 1 | 计数: 15/100
🔇 静音状态  数字值: 0 | 计数: 15/100
```

## 测试步骤

1. **硬件连接**
   - 按照连接表正确连接模块到ESP32C3
   - 确保供电正常（3.3V或5V）

2. **上传代码**
   ```bash
   pio run -t upload
   ```

3. **打开串口监视器**
   ```bash
   pio device monitor
   ```

4. **观察输出**
   - 正常情况下应该看到初始化信息
   - 制造声音（拍手、说话等）观察检测效果

5. **调节灵敏度**
   - 如果太敏感：顺时针旋转电位器降低灵敏度
   - 如果不够敏感：逆时针旋转电位器提高灵敏度

## 故障排除

### 问题1: 无任何输出
**可能原因**: 
- ESP32C3未正确连接或供电
- 串口通信问题

**解决方法**: 
- 检查USB连接和驱动
- 重新上传程序

### 问题2: 始终无声音检测
**可能原因**: 
- 模块未正确连接到GPIO13
- 模块供电不足
- 灵敏度设置过低

**解决方法**: 
- 检查连线，特别是DO到GPIO13的连接
- 确认VCC连接到3.3V或5V
- 调整模块上的电位器提高灵敏度

### 问题3: 持续检测到声音
**可能原因**: 
- 环境噪音过大
- 灵敏度设置过高
- 模块故障

**解决方法**: 
- 在安静环境下测试
- 调整电位器降低灵敏度
- 更换模块

### 问题4: 检测不稳定
**可能原因**: 
- 连线接触不良
- 电源不稳定
- 干扰信号

**解决方法**: 
- 检查并重新连接线路
- 使用稳定的电源
- 远离干扰源

## 性能参数

- **工作电压**: 3.3V - 5V
- **工作电流**: < 5mA
- **检测范围**: 取决于灵敏度设置
- **响应时间**: < 10ms
- **输出电平**: 
  - HIGH: 接近VCC电压
  - LOW: 接近0V

## 进阶测试

如需更详细的测试，可以：
1. 记录不同音量下的模拟值
2. 测试不同频率声音的响应
3. 测试连续工作稳定性
4. 集成到实际应用中测试

## 注意事项

1. 模块对电源质量敏感，建议使用稳定的电源
2. 长时间工作时注意散热
3. 避免在强电磁干扰环境下使用
4. 定期清洁模块表面，避免灰尘影响敏感度
