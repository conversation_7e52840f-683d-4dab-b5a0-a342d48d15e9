# HW181-MIC声音检测测试指南

## 测试目的
验证修复后的声音检测算法是否解决了"一直检测有声音"的问题。

## 测试步骤

### 1. 编译和上传
```bash
pio run -t upload
```

### 2. 启动串口监视器
```bash
pio device monitor
```

### 3. 确保传感器已校准
如果显示"HW181-MIC传感器未校准"，执行：
```
mic_cal
```
等待60秒完成手动校准。

### 4. 静音环境测试（重要！）
- **操作**：保持周围环境绝对安静（不说话、不移动）
- **观察**：连续观察5-10次传感器读数
- **预期**：`sound_detected` 应该始终显示 `0`（未检测到声音）
- **判断**：如果在安静环境下 `sound_detected` 经常显示 `1`，说明问题仍然存在

### 5. 正常说话测试
- **操作**：正常音量说话或制造声音
- **观察**：查看 `sound_detected` 是否变为 `1`
- **预期**：有声音时应该检测到，无声音时应该不检测

### 6. 环境适应性测试
- **操作**：在有持续低级噪声的环境中（如风扇、空调）
- **观察**：传感器是否能适应背景噪声
- **预期**：背景噪声不应该触发声音检测

## 关键观察指标

### 串口输出示例
```
=== 传感器数据 ===
温度: 25.30°C, 气压: 1013.25hPa
分贝: 45.6dB, 声音电压: 1.23V, 模拟值: 2045
声音检测: 0, 校准状态: 是
最小分贝: 43.2dB, 平均分贝: 44.8dB
===
```

### 重点关注
- **声音检测值**：在安静时应该是 `0`
- **分贝值变化**：应该相对稳定，小幅波动正常
- **电压值变化**：应该在合理范围内波动

## 新算法特点

### 改进点
1. **移动平均**：使用最近5次读数的平均值，减少瞬时波动
2. **动态基线**：自动跟踪环境噪声变化
3. **连续检测**：需要连续3次检测到才确认有声音
4. **智能阈值**：基于校准结果动态调整敏感度

### 预期行为
- **启动阶段**：前几次读数可能不稳定（正常）
- **稳定阶段**：在安静环境下声音检测应该稳定为 `0`
- **响应速度**：检测到声音可能需要2-3次读数周期（正常）

## 故障排除

### 如果仍然误报
1. **重新校准**：
   ```
   mic_del_cal  # 删除旧校准数据
   mic_cal      # 重新校准
   ```

2. **检查环境**：
   - 确保校准时环境足够安静
   - 避免电磁干扰源
   - 检查电源稳定性

3. **查看详细信息**：
   ```
   show_mic     # 显示详细传感器信息
   mic_status   # 显示校准状态
   ```

### 如果检测不够敏感
- 可能需要调整算法参数（需要修改代码）
- 重新校准可能有帮助

## 测试报告格式

请记录以下信息：

**环境条件**：
- 测试地点：________
- 环境噪声水平：________ 
- 测试时间：________

**静音测试结果**：
- 连续10次读数的 sound_detected 值：________
- 是否有误报：是/否

**声音检测测试结果**：
- 说话时是否正确检测：是/否
- 响应速度：快/正常/慢

**总体评价**：
- 问题是否解决：是/否
- 其他观察：________

## 技术支持

如果测试中遇到问题，请提供：
1. 完整的串口输出日志
2. 测试环境描述
3. 具体的问题现象

---
更新时间：2024-12-19
