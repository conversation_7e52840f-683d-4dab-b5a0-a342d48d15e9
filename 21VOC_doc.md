# 📦 五合一烟感/空气质量温湿度传感器模组串口协议

---

## 一、通信协议

| 参数   | 值      |
| ---- | ------ |
| 波特率  | `9600` |
| 数据位  | `8 位`  |
| 停止位  | `1 位`  |
| 校验位  | `无`    |
| 通信方式 | 串行通讯方式 |

---

## 二、模组主动上传数据（共 12 个字节）

模块主动发送 12 个字节数据包，结构如下：

| 数据包序   | 名称（单位）          | 描述                            |
| ------ | --------------- | ----------------------------- |
| `0`    | 码头              | 固定值 `0x2C`                    |
| `1-2`  | VOC 空气质量（μg/m³） | `Data[1]*256 + Data[2]`       |
| `3-4`  | 甲醛（μg/m³）       | `Data[3]*256 + Data[4]`       |
| `5-6`  | eCO₂（ppm）       | `Data[5]*256 + Data[6]`       |
| `7-8`  | 温度（单位：0.1°C）    | `Data[7]*256 + Data[8]`       |
| `9-10` | 湿度（单位：0.1% RH）  | `Data[9]*256 + Data[10]`      |
| `11`   | 校验和             | **前 11 个字节累加后的和，取反加 1**（校验字节） |

---

## 三、温湿度数据说明

### 🌡️ 温度数据说明

* **类型：** 16位无符号整数。
* **负数判断规则：**
  若最高位为 `1`（即值 > `0x8000`），则说明是负温度。
  处理方式如下：

  ```
  摄氏温度 = 0xFFFF - 原始值，再加负号。
  ```

  * 例如：`0xFFF5` → `0xFFFF - 0xFFF5 = 0x000A = 10`，因此温度为 `-10°C`。
* **单位换算：**
  实际温度值 = 原始值 × `0.1`

  * 示例：`0x013D = 317` → `317 * 0.1 = 31.7°C`

---

### 💧 湿度数据说明

* 湿度单位：`0.1% RH`
* 湿度值 = `原始值 * 0.1`

---

### ⚠️ 温湿度补偿说明

由于温湿度传感器安装在 PCB 模组上，受到模块本体热影响，建议使用 **外部标准温湿度计进行补偿**。

* 若你系统板读到的温度/湿度和标准设备有差异，应手动进行差值修正。
* 如需帮助，请联系厂家技术支持